"use client";
import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import ProductCard from "@/components/ProductCard";
import { getProducts } from "@/apis/products";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import { ProductData } from "@/types";

interface ProductsSectionProps {
  title: string;
  subtitle?: string;
  limit?: number;
  filter?: string; // "featured", "trending", "new", etc.
  headingLevel?: "h2" | "h3" | "h4"; // For proper heading hierarchy
  sectionId?: string; // For accessibility and SEO
}

const ProductsSection: React.FC<ProductsSectionProps> = ({
  title,
  subtitle,
  limit = 8,
  filter,
  headingLevel = "h2",
  sectionId,
}) => {
  const [products, setProducts] = useState<ProductData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const fetchedProducts = await getProducts(filter);
        setProducts(fetchedProducts.slice(0, limit));
      } catch (error) {
        console.error("Error fetching products:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [filter, limit]);

  // Dynamic heading component for proper hierarchy
  const HeadingComponent = headingLevel;
  const headingId = sectionId ? `${sectionId}-heading` : `${title.toLowerCase().replace(/\s+/g, '-')}-heading`;

  return (
    <section
      className="relative py-16 sm:py-20 lg:py-24 px-4 sm:px-6 lg:px-8 bg-white z-10"
      aria-labelledby={headingId}
      id={sectionId}
    >
      <div className="max-w-7xl mx-auto">
        {/* Section header */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-12 sm:mb-16">
          <div className="max-w-3xl">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <HeadingComponent
                id={headingId}
                className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-3 sm:mb-4 text-gray-900"
              >
                {title}
              </HeadingComponent>
            </motion.div>
            {subtitle && (
              <motion.p
                className="text-gray-600 text-base sm:text-lg leading-relaxed"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                {subtitle}
              </motion.p>
            )}
          </div>

          <Link
            href="/products"
            className="group hidden lg:flex items-center text-accent font-medium mt-6 lg:mt-0 hover:text-accent/80 transition-colors text-base"
            aria-label={`View all ${title.toLowerCase()} products`}
          >
            View All Chiniot Furniture
            <motion.span
              className="inline-block ml-2"
              initial={{ x: 0 }}
              whileHover={{ x: 5 }}
              transition={{ type: "spring", stiffness: 400 }}
            >
              <ArrowRight size={18} />
            </motion.span>
          </Link>
        </div>

        {/* Products grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 sm:gap-8">
            {[...Array(limit > 4 ? 8 : 4)].map((_, index) => (
              <div
                key={index}
                className="bg-gray-100 rounded-lg h-80 sm:h-96 animate-pulse"
              ></div>
            ))}
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 sm:gap-8"
          >
            {products.length > 0 ? (
              products.map((product, index) => (
                <motion.div
                  key={product.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.5,
                    delay: index * 0.1,
                    ease: "easeOut"
                  }}
                >
                  <ProductCard
                    product={product}
                    index={index}
                  />
                </motion.div>
              ))
            ) : (
              <div className="col-span-full text-center py-16 sm:py-20">
                <p className="text-gray-500 text-lg">
                  No products found.
                </p>
              </div>
            )}
          </motion.div>
        )}

        {/* Mobile view all link */}
        <div className="mt-12 sm:mt-16 text-center lg:hidden">
          <Link
            href="/products"
            className="inline-flex items-center text-accent font-medium text-base hover:text-accent/80 transition-colors"
          >
            View All Products
            <ArrowRight size={18} className="ml-2" />
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ProductsSection;
