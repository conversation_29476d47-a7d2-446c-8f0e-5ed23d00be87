import { Metadata } from "next";
import { getProductById } from "@/apis/products";
import { siteConfig, getUrl } from "@/config/site";

// Generate enhanced metadata for product pages with comprehensive SEO
export async function generateMetadata({
  params,
}: {
  params: { id: string };
}): Promise<Metadata> {
  try {
    // Fetch product data
    const product = await getProductById(params.id);

    if (!product) {
      return {
        title: "Product Not Found | Chinioti Wooden Art - Chiniot Furniture Pakistan",
        description: "The requested Chiniot furniture product could not be found. Browse our collection of authentic handcrafted wooden furniture from Chiniot, Punjab, Pakistan.",
        robots: {
          index: false,
          follow: true,
        },
      };
    }

    // Calculate discounted price if available
    const discountedPrice =
      product.discount && product.discount !== "0"
        ? Math.round(
            product.price - product.price * (parseInt(product.discount) / 100)
          )
        : null;

    // Format price for display with currency
    const priceDisplay = discountedPrice
      ? `PKR ${discountedPrice.toLocaleString()} (${product.discount}% off PKR ${product.price.toLocaleString()})`
      : `PKR ${product.price.toLocaleString()}`;

    // Determine product image URL
    const productImage =
      product.images && product.images.length > 0
        ? product.images[0]
        : product.image || getUrl("/og-image.jpg");

    // Ensure image URL is absolute
    const imageUrl = productImage.startsWith("http")
      ? productImage
      : getUrl(productImage);

    // Enhanced title with SEO optimization
    const productTitle = `${product.title || product.name} - ${product.category} | Chiniot Furniture Pakistan`;

    // Enhanced description with local SEO and keywords
    const productDescription = product.description
      ? `${product.description} Authentic ${product.category} furniture handcrafted by skilled artisans in Chiniot, Punjab, Pakistan. ${priceDisplay}. ${product.available ? "Available for immediate delivery" : "Currently out of stock"}.`
      : `Premium ${product.title || product.name} - ${product.category} furniture from Chinioti Wooden Art. Handcrafted with traditional techniques in Chiniot, Punjab, Pakistan. ${priceDisplay}. ${product.available ? "In stock and ready to ship" : "Out of stock"}.`;

    // Enhanced keywords with product-specific and local terms
    const productKeywords = [
      product.title || product.name,
      `${product.category} Chiniot`,
      `${product.category} Pakistan`,
      `handcrafted ${product.category}`,
      `wooden ${product.category}`,
      `traditional ${product.category}`,
      `Chiniot ${product.category}`,
      `Pakistani ${product.category}`,
      `furniture Chiniot Punjab`,
      ...siteConfig.seo.keywords,
    ];

    // Generate comprehensive metadata
    return {
      title: productTitle,
      description: productDescription,
      keywords: productKeywords.filter(Boolean) as string[],
      authors: [{ name: siteConfig.name, url: getUrl() }],
      creator: siteConfig.name,
      publisher: siteConfig.name,

      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          "max-image-preview": "large",
          "max-video-preview": -1,
          "max-snippet": -1,
        },
      },

      openGraph: {
        title: productTitle,
        description: productDescription,
        url: getUrl(`/products/${params.id}`),
        siteName: siteConfig.name,
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: `${product.title || product.name} - ${product.category} furniture from Chiniot, Pakistan`,
          },
        ],
        locale: "en_US",
        type: "website",
        // Enhanced product-specific Open Graph tags
        ...(product.price && {
          "product:price:amount": discountedPrice || product.price,
          "product:price:currency": "PKR",
        }),
        ...(product.available !== undefined && {
          "product:availability": product.available ? "in stock" : "out of stock",
        }),
      },

      twitter: {
        card: "summary_large_image",
        title: productTitle.length > 70 ? `${product.title || product.name} - Chiniot Furniture` : productTitle,
        description: productDescription.length > 200 ? productDescription.substring(0, 197) + "..." : productDescription,
        images: [imageUrl],
        creator: "@chiniotiart",
        site: "@chiniotiart",
      },

      alternates: {
        canonical: getUrl(`/products/${params.id}`),
        languages: {
          "en-US": getUrl(`/products/${params.id}`),
          "en": getUrl(`/products/${params.id}`),
        },
      },

      // Enhanced metadata for better SEO
      other: {
        // Geographic metadata for local SEO
        "geo.region": "PK-PB",
        "geo.placename": "Chiniot, Punjab, Pakistan",
        "geo.position": `${siteConfig.seo.location.latitude};${siteConfig.seo.location.longitude}`,
        "ICBM": `${siteConfig.seo.location.latitude}, ${siteConfig.seo.location.longitude}`,

        // Product-specific metadata
        "product:price": discountedPrice || product.price,
        "product:currency": "PKR",
        "product:availability": product.available ? "in_stock" : "out_of_stock",
        "product:condition": "new",
        "product:brand": siteConfig.name,
        "product:category": product.category,
        "product:material": "Wood",
        "product:origin": "Chiniot, Punjab, Pakistan",

        // Business metadata
        "business:contact_data:locality": siteConfig.contact.address.city,
        "business:contact_data:region": siteConfig.contact.address.region,
        "business:contact_data:country_name": siteConfig.contact.address.country,

        // Content metadata
        "language": "English",
        "locale": "en_US",
        "rating": "General",
        "distribution": "Global",
        "revisit-after": "7 days",
        "copyright": `© ${new Date().getFullYear()} ${siteConfig.name}`,
      },
    };
  } catch (error) {
    console.error("Error generating product metadata:", error);
    return {
      title: `Premium Furniture - ${siteConfig.name} | Chiniot Furniture Pakistan`,
      description: `Explore our premium collection of handcrafted wooden furniture from Chiniot, Punjab, Pakistan. Authentic traditional craftsmanship meets modern design.`,
      keywords: siteConfig.seo.keywords,
    };
  }
}
