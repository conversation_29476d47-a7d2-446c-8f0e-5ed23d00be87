"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { VscChevronDown } from "react-icons/vsc";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DropDownProps, DropdownItem } from "@/types";

const DropDown = ({
  items = [],
  label,
  buttonText,
  defaultSelectedItem,
  onSelectionChange,
}: DropDownProps) => {
  const [selectedItem, setSelectedItem] =
    useState<DropdownItem>(defaultSelectedItem);

  const handleCheckedChange = (checked: boolean, item: DropdownItem) => {
    if (checked) {
      setSelectedItem(item);
      onSelectionChange(item);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="rounded-4xl p-0 h-6 text-xs !bg-acc-primary "
        >
          {buttonText}
          <VscChevronDown />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="bg-transparent">
        <DropdownMenuLabel className="text-xs">{label}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {items.map((item) => (
          <DropdownMenuCheckboxItem
            className="hover:!bg-acc text-xs"
            key={item.id}
            checked={selectedItem.id === item.id}
            onCheckedChange={(checked) => handleCheckedChange(checked, item)}
            disabled={item.disabled}
          >
            {item.label}
          </DropdownMenuCheckboxItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default DropDown;
