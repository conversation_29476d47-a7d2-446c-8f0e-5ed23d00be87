"use client";
import React, { Suspense } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

// Force dynamic rendering to avoid build-time issues
export const dynamic = 'force-dynamic';

// Loading component
const InvalidResetTokenLoading = () => {
  return (
    <div className="bg-card rounded-lg shadow-lg p-8 w-full">
      <div className="text-center">
        <div className="mx-auto w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4 animate-pulse">
          <div className="h-8 w-8 bg-gray-300 rounded"></div>
        </div>
        <div className="h-6 bg-gray-200 rounded mb-2 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded mb-6 animate-pulse"></div>
        <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
  );
};

const InvalidResetTokenContent = () => {
  return (
    <div className="bg-card rounded-lg shadow-lg p-8 w-full">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="text-center"
      >
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4"
        >
          <X className="h-8 w-8 text-destructive" />
        </motion.div>
        
        <h2 className="text-2xl font-bold mb-2">Invalid Reset Link</h2>
        
        <p className="text-muted-foreground mb-6">
          This password reset link is invalid or has expired. Please request a new password reset link.
        </p>
        
        <Link href="/forgot-password">
          <Button variant="default">Request New Reset Link</Button>
        </Link>
      </motion.div>
    </div>
  );
};

const InvalidResetToken = () => {
  return (
    <Suspense fallback={<InvalidResetTokenLoading />}>
      <InvalidResetTokenContent />
    </Suspense>
  );
};

export default InvalidResetToken;
