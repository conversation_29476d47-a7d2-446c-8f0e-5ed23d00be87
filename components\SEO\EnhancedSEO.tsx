"use client";

import { Metadata } from "next";
import <PERSON>ript from "next/script";
import { siteConfig, getUrl } from "@/config/site";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";

interface EnhancedSEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: "website" | "article" | "product";
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  category?: string;
  tags?: string[];
  price?: {
    amount: string;
    currency: string;
  };
  availability?: "InStock" | "OutOfStock" | "PreOrder";
  brand?: string;
  model?: string;
  sku?: string;
  gtin?: string;
  mpn?: string;
  condition?: "NewCondition" | "UsedCondition" | "RefurbishedCondition";
  material?: string[];
  color?: string[];
  dimensions?: {
    length?: string;
    width?: string;
    height?: string;
    weight?: string;
  };
}

/**
 * Enhanced SEO component for comprehensive search engine optimization
 * Includes structured data, Open Graph, Twitter Cards, and local business information
 */
export default function EnhancedSEO({
  title,
  description,
  keywords = [],
  image,
  url,
  type = "website",
  publishedTime,
  modifiedTime,
  author,
  category,
  tags = [],
  price,
  availability = "InStock",
  brand = siteConfig.name,
  model,
  sku,
  gtin,
  mpn,
  condition = "NewCondition",
  material = [],
  color = [],
  dimensions,
}: EnhancedSEOProps) {
  const pathname = usePathname();
  const [currentUrl, setCurrentUrl] = useState("");

  useEffect(() => {
    setCurrentUrl(window.location.href);
  }, [pathname]);

  const pageTitle = title ? `${title} | ${siteConfig.name}` : `${siteConfig.name} - Premium Chiniot Furniture`;
  const pageDescription = description || siteConfig.description;
  const pageUrl = url ? getUrl(url) : currentUrl || getUrl();
  const pageImage = image ? getUrl(image) : getUrl("/og-image.jpg");
  const allKeywords = [...siteConfig.seo.keywords, ...keywords, ...tags];

  // Enhanced breadcrumb generation
  const generateBreadcrumbs = () => {
    const pathSegments = pathname.split('/').filter(Boolean);
    const breadcrumbs = [
      { name: "Home", url: getUrl() }
    ];

    let currentPath = "";
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const name = segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ');
      breadcrumbs.push({
        name: title && index === pathSegments.length - 1 ? title : name,
        url: getUrl(currentPath)
      });
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  // Organization Schema
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "@id": `${getUrl()}/#organization`,
    name: siteConfig.name,
    alternateName: siteConfig.shortName,
    description: siteConfig.description,
    url: getUrl(),
    logo: {
      "@type": "ImageObject",
      url: getUrl("/logo.svg"),
      width: "200",
      height: "60"
    },
    image: pageImage,
    telephone: siteConfig.contact.phone,
    email: siteConfig.contact.email,
    address: {
      "@type": "PostalAddress",
      streetAddress: siteConfig.contact.address.street,
      addressLocality: siteConfig.contact.address.city,
      addressRegion: siteConfig.contact.address.region,
      postalCode: siteConfig.contact.address.postalCode,
      addressCountry: siteConfig.contact.address.country
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: siteConfig.seo.location.latitude,
      longitude: siteConfig.seo.location.longitude
    },
    foundingDate: siteConfig.seo.organization.foundingDate,
    areaServed: siteConfig.seo.organization.areaServed,
    paymentAccepted: siteConfig.seo.organization.paymentAccepted,
    currenciesAccepted: siteConfig.seo.organization.currenciesAccepted,
    priceRange: siteConfig.seo.organization.priceRange,
    sameAs: [
      siteConfig.social.facebook,
      siteConfig.social.instagram,
      siteConfig.social.twitter,
      siteConfig.social.youtube
    ]
  };

  // Local Business Schema
  const localBusinessSchema = {
    "@context": "https://schema.org",
    "@type": "FurnitureStore",
    "@id": `${getUrl()}/#localbusiness`,
    name: siteConfig.name,
    image: pageImage,
    url: getUrl(),
    telephone: siteConfig.contact.phone,
    email: siteConfig.contact.email,
    address: {
      "@type": "PostalAddress",
      streetAddress: siteConfig.contact.address.street,
      addressLocality: siteConfig.contact.address.city,
      addressRegion: siteConfig.contact.address.region,
      postalCode: siteConfig.contact.address.postalCode,
      addressCountry: siteConfig.contact.address.country
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: siteConfig.seo.location.latitude,
      longitude: siteConfig.seo.location.longitude
    },
    openingHoursSpecification: [
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
        opens: siteConfig.seo.businessHours.weekdays.open,
        closes: siteConfig.seo.businessHours.weekdays.close
      },
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: "Saturday",
        opens: siteConfig.seo.businessHours.saturday.open,
        closes: siteConfig.seo.businessHours.saturday.close
      },
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: "Sunday",
        opens: siteConfig.seo.businessHours.sunday.open,
        closes: siteConfig.seo.businessHours.sunday.close
      }
    ],
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: siteConfig.seo.richSnippets.aggregateRating.ratingValue,
      reviewCount: siteConfig.seo.richSnippets.aggregateRating.reviewCount,
      bestRating: siteConfig.seo.richSnippets.aggregateRating.bestRating,
      worstRating: siteConfig.seo.richSnippets.aggregateRating.worstRating
    },
    priceRange: siteConfig.seo.organization.priceRange,
    paymentAccepted: siteConfig.seo.organization.paymentAccepted,
    currenciesAccepted: siteConfig.seo.organization.currenciesAccepted,
    areaServed: siteConfig.seo.organization.areaServed,
    sameAs: [
      siteConfig.social.facebook,
      siteConfig.social.instagram,
      siteConfig.social.twitter,
      siteConfig.social.youtube
    ]
  };

  // Website Schema
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "@id": `${getUrl()}/#website`,
    name: siteConfig.name,
    alternateName: siteConfig.shortName,
    description: siteConfig.description,
    url: getUrl(),
    publisher: {
      "@id": `${getUrl()}/#organization`
    },
    potentialAction: {
      "@type": "SearchAction",
      target: {
        "@type": "EntryPoint",
        urlTemplate: `${getUrl()}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    inLanguage: "en-US",
    copyrightYear: new Date().getFullYear(),
    copyrightHolder: {
      "@id": `${getUrl()}/#organization`
    }
  };

  // Enhanced Breadcrumb Schema
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url
    }))
  };

  // Enhanced Article Schema for blog posts
  const articleSchema = type === "article" ? {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": pageTitle,
    "description": pageDescription,
    "image": pageImage,
    "author": {
      "@type": "Person",
      "name": author || siteConfig.name
    },
    "publisher": {
      "@id": `${getUrl()}/#organization`
    },
    "datePublished": publishedTime,
    "dateModified": modifiedTime || publishedTime,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": pageUrl
    }
  } : null;

  return (
    <>
      {/* Organization Schema */}
      <Script
        id="organization-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />

      {/* Local Business Schema */}
      <Script
        id="local-business-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(localBusinessSchema)
        }}
      />

      {/* Website Schema */}
      <Script
        id="website-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema)
        }}
      />

      {/* Breadcrumb Schema */}
      <Script
        id="breadcrumb-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbSchema)
        }}
      />

      {/* Article Schema for blog posts */}
      {articleSchema && (
        <Script
          id="article-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(articleSchema)
          }}
        />
      )}

      {/* Additional Meta Tags */}
      <meta name="keywords" content={allKeywords.join(", ")} />
      <meta name="author" content={author || siteConfig.name} />
      <meta name="publisher" content={siteConfig.name} />
      <meta name="copyright" content={`© ${new Date().getFullYear()} ${siteConfig.name}`} />
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="bingbot" content="index, follow" />
      
      {/* Geographic Meta Tags */}
      <meta name="geo.region" content="PK-PB" />
      <meta name="geo.placename" content="Chiniot, Punjab, Pakistan" />
      <meta name="geo.position" content={`${siteConfig.seo.location.latitude};${siteConfig.seo.location.longitude}`} />
      <meta name="ICBM" content={`${siteConfig.seo.location.latitude}, ${siteConfig.seo.location.longitude}`} />
      
      {/* Business Meta Tags */}
      <meta name="business:contact_data:street_address" content={siteConfig.contact.address.street} />
      <meta name="business:contact_data:locality" content={siteConfig.contact.address.city} />
      <meta name="business:contact_data:region" content={siteConfig.contact.address.region} />
      <meta name="business:contact_data:postal_code" content={siteConfig.contact.address.postalCode} />
      <meta name="business:contact_data:country_name" content={siteConfig.contact.address.country} />
      <meta name="business:contact_data:phone_number" content={siteConfig.contact.phone} />
      <meta name="business:contact_data:email" content={siteConfig.contact.email} />
      
      {/* Language and Locale */}
      <meta name="language" content="English" />
      <meta name="locale" content="en_US" />
      
      {/* Content Classification */}
      <meta name="rating" content="General" />
      <meta name="distribution" content="Global" />
      <meta name="revisit-after" content="7 days" />
      
      {/* Mobile Optimization */}
      <meta name="format-detection" content="telephone=yes" />
      <meta name="format-detection" content="address=yes" />
      
      {/* Social Media Optimization */}
      <meta property="business:hours:day" content="monday" />
      <meta property="business:hours:start" content={siteConfig.seo.businessHours.weekdays.open} />
      <meta property="business:hours:end" content={siteConfig.seo.businessHours.weekdays.close} />
    </>
  );
}
