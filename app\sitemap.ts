import { MetadataRoute } from 'next';
import { siteConfig } from '@/config/site';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Base URL for your site
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || `https://${siteConfig.seo.domain}`;

  console.log('Generating sitemap for:', baseUrl);

  // High priority static routes for SEO - optimized for Chiniot furniture
  const staticRoutes = [
    {
      url: `${baseUrl}`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1.0,
    },
    {
      url: `${baseUrl}/products`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/about-us`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/video-blogs`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/blogs`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.6,
    },
    // SEO-focused category pages for Chiniot furniture
    {
      url: `${baseUrl}/categories/beds`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/categories/tables`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/categories/chairs`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/categories/wardrobes`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/categories/dining-sets`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/categories/sofa-sets`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/categories/cabinets`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/categories/dressing-tables`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    },
    // Legal and policy pages
    {
      url: `${baseUrl}/privacy-policy`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/terms-of-service`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/shipping-policy`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/return-policy`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
  ];

  // Dynamic routes for products
  let productRoutes: MetadataRoute.Sitemap = [];
  let categoryRoutes: MetadataRoute.Sitemap = [];
  let blogRoutes: MetadataRoute.Sitemap = [];

  // Only fetch from API if backend URL is available and we're not in build mode
  const backendUrl = process.env.BACKEND_BASE_URL;
  const isBuilding = process.env.NODE_ENV === 'production' && !process.env.VERCEL;

  if (backendUrl && !isBuilding) {
    try {
      console.log('Fetching products from API...');
      // Fetch products from your API
      const productsResponse = await fetch(`${backendUrl}/api/products`, {
        next: { revalidate: 3600 }, // Revalidate every hour
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (productsResponse.ok) {
        const responseData = await productsResponse.json();

        // Extract products array from the API response structure
        const products = responseData?.data?.products || [];

        // Ensure products is an array before calling map
        if (Array.isArray(products)) {
          console.log(`Found ${products.length} products for sitemap`);
          // Create sitemap entries for each product with SEO optimization
          productRoutes = products.map((product: any) => ({
            url: `${baseUrl}/products/${product.id || product._id}`,
            lastModified: new Date(product.updatedAt || product.createdAt || new Date()),
            changeFrequency: 'weekly' as const,
            priority: 0.8,
          }));
        }
      } else {
        console.warn(`Products API returned status: ${productsResponse.status}`);
      }
    } catch (error) {
      console.error('Error fetching products for sitemap:', error);
    }
  } else {
    console.log('Skipping API calls during build or missing backend URL');
  }

  if (backendUrl && !isBuilding) {
    try {
      console.log('Fetching categories from API...');
      // Fetch categories from your API
      const categoriesResponse = await fetch(`${backendUrl}/api/categories`, {
        next: { revalidate: 86400 }, // Revalidate daily
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (categoriesResponse.ok) {
        const responseData = await categoriesResponse.json();

        // Extract categories array from the API response structure
        const categories = responseData?.data?.categories || responseData || [];

        // Ensure categories is an array before calling map
        if (Array.isArray(categories)) {
          console.log(`Found ${categories.length} categories for sitemap`);
          // Create sitemap entries for each category
          categoryRoutes = categories.map((category: any) => ({
            url: `${baseUrl}/categories/${category.slug || category.id || category._id}`,
            lastModified: new Date(category.updatedAt || category.createdAt || new Date()),
            changeFrequency: 'weekly' as const,
            priority: 0.7,
          }));
        }
      } else {
        console.warn(`Categories API returned status: ${categoriesResponse.status}`);
      }
    } catch (error) {
      console.error('Error fetching categories for sitemap:', error);
    }
  }

  if (backendUrl && !isBuilding) {
    try {
      console.log('Fetching video blogs from API...');
      // Fetch blog posts/VLOGS from your API
      const blogsResponse = await fetch(`${backendUrl}/api/video-blogs`, {
        next: { revalidate: 3600 }, // Revalidate every hour
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (blogsResponse.ok) {
        const responseData = await blogsResponse.json();

        // Extract video blogs array from the API response structure
        const blogs = responseData?.data?.videoBlogs || [];

        // Ensure blogs is an array before calling map
        if (Array.isArray(blogs)) {
          console.log(`Found ${blogs.length} video blogs for sitemap`);
          // Create sitemap entries for each blog post
          blogRoutes = blogs.map((blog: any) => ({
            url: `${baseUrl}/video-blogs/${blog.id || blog._id}`,
            lastModified: new Date(blog.updatedAt || blog.createdAt || new Date()),
            changeFrequency: 'monthly' as const,
            priority: 0.5,
          }));
        }
      } else {
        console.warn(`Video blogs API returned status: ${blogsResponse.status}`);
      }
    } catch (error) {
      console.error('Error fetching blogs for sitemap:', error);
    }
  }

  // Combine all routes and sort by priority
  const allRoutes = [...staticRoutes, ...productRoutes, ...categoryRoutes, ...blogRoutes];

  // Sort by priority (highest first) for better SEO
  allRoutes.sort((a, b) => (b.priority || 0) - (a.priority || 0));

  console.log(`Generated sitemap with ${allRoutes.length} total routes:`, {
    static: staticRoutes.length,
    products: productRoutes.length,
    categories: categoryRoutes.length,
    blogs: blogRoutes.length,
  });

  return allRoutes;
}
