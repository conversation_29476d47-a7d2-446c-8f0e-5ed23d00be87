"use client";

import { useEffect, useCallback, useState } from "react";
import <PERSON>ript from "next/script";
import { siteConfig } from "@/config/site";

// Declare gtag function for Google Analytics
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}

// Type for gtag function
const gtag = typeof window !== 'undefined' ? window.gtag : undefined;

// Types for web-vitals
interface Metric {
  id: string;
  name: string;
  value: number;
  delta: number;
  entries: any[];
}

interface PerformanceMetrics {
  lcp: number | null;
  fid: number | null;
  cls: number | null;
  fcp: number | null;
  ttfb: number | null;
}

interface AdvancedPerformanceOptimizerProps {
  enableWebVitalsTracking?: boolean;
  enableResourceHints?: boolean;
  enableCriticalResourcePreload?: boolean;
  enableServiceWorker?: boolean;
  criticalResources?: string[];
}

/**
 * Advanced Performance Optimizer Component
 * Implements cutting-edge performance optimizations for Core Web Vitals
 */
export default function AdvancedPerformanceOptimizer({
  enableWebVitalsTracking = true,
  enableResourceHints = true,
  enableCriticalResourcePreload = true,
  enableServiceWorker = false,
  criticalResources = [
    "/logo.svg",
    "/home-background.svg",
    "/assets/backgrounds/home-bg.png"
  ],
}: AdvancedPerformanceOptimizerProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    lcp: null,
    fid: null,
    cls: null,
    fcp: null,
    ttfb: null,
  });

  // Advanced resource preloading strategy
  const preloadCriticalResources = useCallback(() => {
    if (!enableCriticalResourcePreload) return;

    criticalResources.forEach((resource) => {
      const link = document.createElement("link");
      link.rel = "preload";
      link.href = resource;
      
      // Determine resource type based on extension
      if (resource.endsWith(".svg") || resource.endsWith(".png") || resource.endsWith(".jpg") || resource.endsWith(".webp")) {
        link.as = "image";
        if (resource.endsWith(".svg")) {
          link.type = "image/svg+xml";
        }
      } else if (resource.endsWith(".css")) {
        link.as = "style";
      } else if (resource.endsWith(".js")) {
        link.as = "script";
      }
      
      // Add crossorigin for external resources
      if (resource.startsWith("http")) {
        link.crossOrigin = "anonymous";
      }
      
      document.head.appendChild(link);
    });
  }, [enableCriticalResourcePreload, criticalResources]);

  // Optimize CSS delivery
  const optimizeCSSDelivery = useCallback(() => {
    // Load non-critical CSS asynchronously
    const loadCSS = (href: string) => {
      const link = document.createElement("link");
      link.rel = "preload";
      link.as = "style";
      link.href = href;
      link.onload = () => {
        link.rel = "stylesheet";
      };
      document.head.appendChild(link);
    };

    // Example: Load non-critical stylesheets
    // loadCSS("/styles/non-critical.css");
  }, []);

  // Advanced image optimization
  const optimizeImages = useCallback(() => {
    // Implement advanced lazy loading with Intersection Observer
    const images = document.querySelectorAll("img[data-src]");
    
    if ("IntersectionObserver" in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            img.src = img.dataset.src || "";
            img.classList.remove("lazy");
            img.classList.add("loaded");
            imageObserver.unobserve(img);
          }
        });
      }, {
        rootMargin: "50px 0px",
        threshold: 0.01,
      });

      images.forEach((img) => imageObserver.observe(img));
    }
  }, []);

  // Reduce JavaScript execution time
  const optimizeJavaScript = useCallback(() => {
    // Implement code splitting for heavy components
    const loadComponentLazily = async (componentName: string) => {
      try {
        const module = await import(`@/components/${componentName}`);
        return module.default;
      } catch (error) {
        console.warn(`Failed to load component ${componentName}:`, error);
        return null;
      }
    };

    // Example usage:
    // loadComponentLazily("HeavyComponent");
  }, []);

  // Optimize third-party scripts
  const optimizeThirdPartyScripts = useCallback(() => {
    // Delay non-critical third-party scripts
    const delayedScripts: Array<{ src: string; delay: number }> = [
      // Add your third-party scripts here
      // { src: "https://example.com/script.js", delay: 3000 }
    ];

    delayedScripts.forEach(({ src, delay }) => {
      setTimeout(() => {
        const script = document.createElement("script");
        script.src = src;
        script.async = true;
        document.head.appendChild(script);
      }, delay);
    });
  }, []);

  // Web Vitals tracking
  const trackWebVitals = useCallback(() => {
    if (!enableWebVitalsTracking || typeof window === "undefined") return;

    // Dynamic import of web-vitals to reduce bundle size
    import("web-vitals").then(({ getCLS, getFID, getFCP, getLCP, getTTFB }: any) => {
      getCLS((metric: Metric) => {
        setMetrics((prev) => ({ ...prev, cls: metric.value }));
        // Send to analytics
        if (gtag) {
          gtag("event", "CLS", {
            event_category: "Web Vitals",
            event_label: metric.id,
            value: Math.round(metric.value * 1000),
            non_interaction: true,
          });
        }
      });

      getFID((metric: Metric) => {
        setMetrics((prev) => ({ ...prev, fid: metric.value }));
        if (gtag) {
          gtag("event", "FID", {
            event_category: "Web Vitals",
            event_label: metric.id,
            value: Math.round(metric.value),
            non_interaction: true,
          });
        }
      });

      getFCP((metric: Metric) => {
        setMetrics((prev) => ({ ...prev, fcp: metric.value }));
      });

      getLCP((metric: Metric) => {
        setMetrics((prev) => ({ ...prev, lcp: metric.value }));
        if (gtag) {
          gtag("event", "LCP", {
            event_category: "Web Vitals",
            event_label: metric.id,
            value: Math.round(metric.value),
            non_interaction: true,
          });
        }
      });

      getTTFB((metric: Metric) => {
        setMetrics((prev) => ({ ...prev, ttfb: metric.value }));
      });
    }).catch((error) => {
      console.warn("Failed to load web-vitals:", error);
    });
  }, [enableWebVitalsTracking]);

  // Initialize optimizations
  useEffect(() => {
    // Run optimizations after component mount
    const initOptimizations = () => {
      preloadCriticalResources();
      optimizeCSSDelivery();
      optimizeImages();
      optimizeJavaScript();
      optimizeThirdPartyScripts();
      trackWebVitals();
    };

    // Delay initialization to avoid blocking main thread
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", initOptimizations);
    } else {
      setTimeout(initOptimizations, 0);
    }

    return () => {
      document.removeEventListener("DOMContentLoaded", initOptimizations);
    };
  }, [
    preloadCriticalResources,
    optimizeCSSDelivery,
    optimizeImages,
    optimizeJavaScript,
    optimizeThirdPartyScripts,
    trackWebVitals,
  ]);

  return (
    <>
      {/* Resource Hints */}
      {enableResourceHints && (
        <>
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
          <link rel="dns-prefetch" href="//www.google-analytics.com" />
          <link rel="dns-prefetch" href="//www.googletagmanager.com" />
          <link rel="dns-prefetch" href="//fonts.googleapis.com" />
          <link rel="dns-prefetch" href="//fonts.gstatic.com" />
        </>
      )}

      {/* Critical CSS */}
      <style jsx>{`
        /* Critical above-the-fold styles */
        .hero-section {
          contain: layout style paint;
          will-change: transform;
        }
        
        /* Optimize font loading */
        @font-face {
          font-family: 'Poppins';
          font-style: normal;
          font-weight: 400;
          font-display: swap;
          src: local('Poppins Regular'), local('Poppins-Regular');
        }
        
        /* Prevent layout shift */
        img, video {
          max-width: 100%;
          height: auto;
        }
        
        /* Lazy loading styles */
        .lazy {
          opacity: 0;
          transition: opacity 0.3s ease-in-out;
        }
        
        .lazy.loaded {
          opacity: 1;
        }
        
        /* Optimize animations */
        .animate-optimized {
          will-change: transform;
          transform: translateZ(0);
        }
      `}</style>

      {/* Service Worker Registration */}
      {enableServiceWorker && (
        <Script
          id="service-worker"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `,
          }}
        />
      )}

      {/* Performance monitoring in development */}
      {process.env.NODE_ENV === "development" && (
        <div
          style={{
            position: "fixed",
            top: 10,
            right: 10,
            background: "rgba(0,0,0,0.8)",
            color: "white",
            padding: "10px",
            borderRadius: "5px",
            fontSize: "12px",
            zIndex: 9999,
            fontFamily: "monospace",
          }}
        >
          <div>LCP: {metrics.lcp ? `${Math.round(metrics.lcp)}ms` : "..."}</div>
          <div>FID: {metrics.fid ? `${Math.round(metrics.fid)}ms` : "..."}</div>
          <div>CLS: {metrics.cls ? metrics.cls.toFixed(3) : "..."}</div>
          <div>FCP: {metrics.fcp ? `${Math.round(metrics.fcp)}ms` : "..."}</div>
          <div>TTFB: {metrics.ttfb ? `${Math.round(metrics.ttfb)}ms` : "..."}</div>
        </div>
      )}
    </>
  );
}

// Export performance utilities
export const performanceUtils = {
  // Preload critical resources
  preloadResource: (href: string, as: string, type?: string) => {
    const link = document.createElement("link");
    link.rel = "preload";
    link.href = href;
    link.as = as;
    if (type) link.type = type;
    document.head.appendChild(link);
  },

  // Lazy load component - Note: Dynamic imports with variables are not supported in Next.js
  // This function is kept for API compatibility but will not work with dynamic paths
  lazyLoadComponent: async (componentPath: string) => {
    console.warn(`Dynamic component loading not supported in Next.js build: ${componentPath}`);
    return null;
  },

  // Optimize image loading
  optimizeImageLoading: (img: HTMLImageElement) => {
    img.loading = "lazy";
    img.decoding = "async";
    if (img.dataset.src) {
      img.src = img.dataset.src;
    }
  },
};
