"use client";
import { getCategories } from "@/apis/categories";
import CategoryCard from "@/components/CategoryCard";
import { Category } from "@/types";
import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

const Categories = () => {
  const [categories, setCategories] = useState<Category[]>([]);

  useEffect(() => {
    setCategories(getCategories());
  }, []);

  return (
    // <section className="py-16 px-4 md:px-8">
    //   <div className="container mx-auto">
    //     <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-10">
    //       <motion.div
    //         initial={{ opacity: 0, y: -20 }}
    //         animate={{ opacity: 1, y: 0 }}
    //         transition={{ duration: 0.5 }}
    //         className="max-w-xl"
    //       >
    //         <h2 className="text-2xl md:text-3xl font-bold mb-3">
    //           Shop by Category
    //         </h2>
    //         <p className="text-gray-600">
    //           Explore our curated selection of furniture, lighting, décor and
    //           tabletop handcrafted by the best Chinioti artisans.
    //         </p>
    //       </motion.div>

    //       <Link
    //         href="/products"
    //         className="group hidden md:flex items-center text-accent font-medium mt-4 md:mt-0"
    //       >
    //         View All Products
    //         <motion.span
    //           className="inline-block ml-1"
    //           initial={{ x: 0 }}
    //           whileHover={{ x: 5 }}
    //           transition={{ type: "spring", stiffness: 400 }}
    //         >
    //           <ArrowRight size={16} />
    //         </motion.span>
    //       </Link>
    //     </div>

    //     {/* Desktop view - Grid layout */}
    //     <div className="hidden md:block">
    //       <motion.div
    //         className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6 justify-between"
    //         initial={{ opacity: 0, y: 20 }}
    //         animate={{ opacity: 1, y: 0 }}
    //         transition={{ duration: 0.5, delay: 0.2 }}
    //       >
    //         {categories.map((item, index) => (
    //           <motion.div
    //             key={item.id}
    //             initial={{ opacity: 0, y: 20 }}
    //             animate={{ opacity: 1, y: 0 }}
    //             transition={{ duration: 0.3, delay: 0.1 + index * 0.05 }}
    //             className="h-full"
    //           >
    //             <div className="h-full rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300">
    //               <CategoryCard {...item} />
    //             </div>
    //           </motion.div>
    //         ))}
    //       </motion.div>
    //     </div>

    //     {/* Mobile view - Slider */}
    //     <div className="md:hidden">
    //       <Swiper
    //         modules={[Navigation, Pagination]}
    //         spaceBetween={16}
    //         slidesPerView={1.2}
    //         centeredSlides={false}
    //         pagination={{ clickable: true }}
    //         className="w-full"
    //       >
    //         {categories.map((item) => (
    //           <SwiperSlide key={item.id} className="pb-10">
    //             <div className="rounded-lg overflow-hidden  h-full">
    //               <CategoryCard {...item} />
    //             </div>
    //           </SwiperSlide>
    //         ))}
    //       </Swiper>
    //     </div>

    //     <div className="mt-8 text-center md:hidden">
    //       <Link
    //         href="/products"
    //         className="inline-flex items-center text-accent font-medium"
    //       >
    //         View All Categories
    //         <ArrowRight size={16} className="ml-1" />
    //       </Link>
    //     </div>
    //   </div>
    // </section>
    <></>
  );
};

export default Categories;
