import React from "react";
import { But<PERSON> } from "../components/ui/button";
import { secondaryFont } from "@/constants/fonts";
import Link from "next/link";
import Image from "next/image";
import HeroHeader from "../components/HeroHeader";

const Hero = () => {
  return (
    <section
      className="relative bg-cover bg-no-repeat bg-center w-full min-h-screen z-0 isolate"
      aria-labelledby="hero-heading"
      role="banner"
    >
      {/* Background Image with proper alt text for SEO */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/home-background.svg"
          alt="Traditional Chinioti wooden furniture craftsman creating handcrafted furniture in Chiniot, Punjab, Pakistan workshop"
          fill
          className="object-cover"
          priority
          sizes="100vw"
        />
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-black/20 z-10"></div>
      </div>

      {/* Header integrated at the top with proper z-index */}
      <div className="relative z-50">
        <HeroHeader />
      </div>

      {/* Hero content container */}
      <div className="relative flex items-center justify-center min-h-[calc(100vh-80px)] px-4 sm:px-6 lg:px-8 py-8 sm:py-12 text-center z-20">
        <div className="max-w-7xl mx-auto w-full">
          <div className="grid place-items-center h-full gap-6 sm:gap-8 md:gap-10 lg:gap-12">
            <header className="space-y-4 sm:space-y-6">
              <h1
                id="hero-heading"
                className={
                  secondaryFont +
                  " text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl font-semibold font-secondary text-white leading-tight drop-shadow-2xl"
                }
              >
                <span className="block">Discover the World of</span>
                <span className="block text-accent drop-shadow-2xl">Authentic Chinioti</span>
                <span className="block text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl mt-2">
                  Handcrafted Wooden Furniture
                </span>
              </h1>
            </header>

            <div className="flex justify-center items-center gap-2 sm:gap-3 md:gap-4">
              <Link
                href="/products"
                aria-label="Browse our collection of handcrafted Chiniot furniture"
              >
                <Button
                variant="outline"
                className="bg-[#d3691e] cursor-pointer text-white px-3 sm:px-4 md:px-5 py-1.5 sm:py-2 md:py-2.5 text-[10px] sm:text-xs md:text-sm border-white"
              >
                Explore Our Collection →
              </Button>
              </Link>
              <Link
                href="/contact"
                aria-label="Contact us for custom furniture orders"
              >
                <Button
                  variant="outline"
                  className="border-white hover:border-white/80 text-white bg-transparent hover:bg-white/10 cursor-pointer px-3 sm:px-4 md:px-5 py-1.5 sm:py-2 md:py-2.5 text-[10px] sm:text-xs md:text-sm "
                >
                  Custom Orders
                </Button>
              </Link>
            </div>

            {/* Trust indicators */}
            <div className="grid grid-cols-2 lg:flex lg:flex-wrap gap-4 sm:gap-6 md:gap-8 text-white text-sm sm:text-base w-full max-w-4xl justify-items-center justify-center">
              <div className="flex items-center gap-2 sm:gap-3">
                <span className="text-[#d3691e] text-lg sm:text-xl">✓</span>
                <span className="drop-shadow-lg font-medium">500+ Years Heritage</span>
              </div>
              <div className="flex items-center gap-2 sm:gap-3">
                <span className="text-[#d3691e] text-lg sm:text-xl">✓</span>
                <span className="drop-shadow-lg font-medium">Worldwide Shipping</span>
              </div>
              <div className="flex items-center gap-2 sm:gap-3">
                <span className="text-[#d3691e] text-lg sm:text-xl">✓</span>
                <span className="drop-shadow-lg font-medium">Authentic Craftsmanship</span>
              </div>
              <div className="flex items-center gap-2 sm:gap-3">
                <span className="text-[#d3691e] text-lg sm:text-xl">✓</span>
                <span className="drop-shadow-lg font-medium">Made in Chiniot, Pakistan</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
