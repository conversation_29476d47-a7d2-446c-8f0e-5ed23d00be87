"use client";

import { useEffect, useCallback, useRef } from "react";
import Script from "next/script";

interface CoreWebVitalsEnhancerProps {
  enableLCPOptimization?: boolean;
  enableFIDOptimization?: boolean;
  enableCLSOptimization?: boolean;
  enableResourceHints?: boolean;
  criticalResources?: string[];
  preloadFonts?: string[];
}

/**
 * Advanced Core Web Vitals Enhancer
 * Implements cutting-edge optimizations for LCP, FID, and CLS
 */
export default function CoreWebVitalsEnhancer({
  enableLCPOptimization = true,
  enableFIDOptimization = true,
  enableCLSOptimization = true,
  enableResourceHints = true,
  criticalResources = [
    "/logo.svg",
    "/home-background.svg",
    "/og-image.jpg"
  ],
  preloadFonts = [
    "https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap"
  ],
}: CoreWebVitalsEnhancerProps) {
  const observerRef = useRef<IntersectionObserver | null>(null);
  const mutationObserverRef = useRef<MutationObserver | null>(null);

  // LCP Optimization: Preload critical resources and optimize images
  const optimizeLCP = useCallback(() => {
    if (!enableLCPOptimization) return;

    // Preload critical resources
    criticalResources.forEach((resource) => {
      const link = document.createElement("link");
      link.rel = "preload";
      link.href = resource;
      
      if (resource.endsWith(".svg") || resource.endsWith(".png") || resource.endsWith(".jpg") || resource.endsWith(".webp")) {
        link.as = "image";
        if (resource.endsWith(".svg")) {
          link.type = "image/svg+xml";
        }
      } else if (resource.endsWith(".css")) {
        link.as = "style";
      } else if (resource.endsWith(".js")) {
        link.as = "script";
      }
      
      document.head.appendChild(link);
    });

    // Optimize hero images with fetchpriority
    const heroImages = document.querySelectorAll('img[data-hero], .hero-section img, [data-priority="high"] img');
    heroImages.forEach((img) => {
      if (img instanceof HTMLImageElement) {
        img.fetchPriority = "high";
        img.loading = "eager";
        img.decoding = "sync";
      }
    });

    // Preload fonts
    preloadFonts.forEach((fontUrl) => {
      const link = document.createElement("link");
      link.rel = "preload";
      link.href = fontUrl;
      link.as = "style";
      link.crossOrigin = "anonymous";
      document.head.appendChild(link);
    });

    // Optimize background images
    const elementsWithBgImages = document.querySelectorAll('[style*="background-image"]');
    elementsWithBgImages.forEach((element) => {
      const bgImage = getComputedStyle(element).backgroundImage;
      const urlMatch = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
      if (urlMatch && urlMatch[1]) {
        const link = document.createElement("link");
        link.rel = "preload";
        link.href = urlMatch[1];
        link.as = "image";
        document.head.appendChild(link);
      }
    });
  }, [enableLCPOptimization, criticalResources, preloadFonts]);

  // FID Optimization: Reduce JavaScript execution time and optimize interactions
  const optimizeFID = useCallback(() => {
    if (!enableFIDOptimization) return;

    // Defer non-critical JavaScript
    const deferNonCriticalJS = () => {
      const scripts = document.querySelectorAll('script[data-defer]');
      scripts.forEach((script) => {
        if (script instanceof HTMLScriptElement) {
          script.defer = true;
        }
      });
    };

    // Optimize event listeners with passive option
    const optimizeEventListeners = () => {
      const elements = document.querySelectorAll('[data-optimize-events]');
      elements.forEach((element) => {
        // Replace existing event listeners with passive ones where appropriate
        const events = ['scroll', 'wheel', 'touchstart', 'touchmove'];
        events.forEach((eventType) => {
          element.addEventListener(eventType, (e) => {
            // Passive event listener
          }, { passive: true });
        });
      });
    };

    // Break up long tasks
    const breakUpLongTasks = () => {
      // Use scheduler.postTask if available, otherwise setTimeout
      const scheduleTask = (callback: () => void, priority: 'user-blocking' | 'user-visible' | 'background' = 'user-visible') => {
        if ('scheduler' in window && 'postTask' in (window as any).scheduler) {
          (window as any).scheduler.postTask(callback, { priority });
        } else {
          setTimeout(callback, 0);
        }
      };

      // Example: Break up heavy initialization
      const heavyTasks = document.querySelectorAll('[data-heavy-task]');
      heavyTasks.forEach((element, index) => {
        scheduleTask(() => {
          // Process heavy task
          element.classList.add('processed');
        }, index < 3 ? 'user-visible' : 'background');
      });
    };

    deferNonCriticalJS();
    optimizeEventListeners();
    breakUpLongTasks();
  }, [enableFIDOptimization]);

  // CLS Optimization: Prevent layout shifts
  const optimizeCLS = useCallback(() => {
    if (!enableCLSOptimization) return;

    // Set explicit dimensions for images without them
    const setImageDimensions = () => {
      const images = document.querySelectorAll('img:not([width]):not([height])');
      images.forEach((img) => {
        if (img instanceof HTMLImageElement) {
          // Use aspect ratio to prevent layout shift
          img.style.aspectRatio = 'auto';
          img.style.width = '100%';
          img.style.height = 'auto';
          
          // Set dimensions once image loads
          img.onload = () => {
            if (img.naturalWidth && img.naturalHeight) {
              img.width = img.naturalWidth;
              img.height = img.naturalHeight;
            }
          };
        }
      });
    };

    // Reserve space for dynamic content
    const reserveSpaceForDynamicContent = () => {
      const dynamicElements = document.querySelectorAll('[data-dynamic-content]');
      dynamicElements.forEach((element) => {
        if (element instanceof HTMLElement) {
          // Set minimum height to prevent layout shift
          const minHeight = element.dataset.minHeight || '200px';
          element.style.minHeight = minHeight;
          element.style.contain = 'layout';
        }
      });
    };

    // Optimize font loading to prevent FOIT/FOUT
    const optimizeFontLoading = () => {
      // Add font-display: swap to all font faces
      const style = document.createElement('style');
      style.textContent = `
        @font-face {
          font-display: swap;
        }
      `;
      document.head.appendChild(style);
    };

    // Monitor layout shifts
    const monitorLayoutShifts = () => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.entryType === 'layout-shift' && !(entry as any).hadRecentInput) {
              const shift = entry as any;
              if (shift.value > 0.1) {
                console.warn('Large layout shift detected:', {
                  value: shift.value,
                  sources: shift.sources,
                  startTime: shift.startTime
                });
              }
            }
          });
        });
        
        observer.observe({ entryTypes: ['layout-shift'] });
      }
    };

    setImageDimensions();
    reserveSpaceForDynamicContent();
    optimizeFontLoading();
    monitorLayoutShifts();
  }, [enableCLSOptimization]);

  // Advanced image lazy loading with intersection observer
  const setupAdvancedLazyLoading = useCallback(() => {
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            
            // Load image
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
            }
            
            // Load srcset
            if (img.dataset.srcset) {
              img.srcset = img.dataset.srcset;
              img.removeAttribute('data-srcset');
            }
            
            // Add loaded class
            img.classList.add('loaded');
            img.classList.remove('lazy');
            
            // Stop observing
            observerRef.current?.unobserve(img);
          }
        });
      },
      {
        rootMargin: '50px 0px',
        threshold: 0.01,
      }
    );

    // Observe all lazy images
    const lazyImages = document.querySelectorAll('img[data-src], img.lazy');
    lazyImages.forEach((img) => {
      observerRef.current?.observe(img);
    });
  }, []);

  // Monitor DOM changes for new lazy images
  const setupMutationObserver = useCallback(() => {
    if (mutationObserverRef.current) {
      mutationObserverRef.current.disconnect();
    }

    mutationObserverRef.current = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            
            // Check for new lazy images
            const lazyImages = element.querySelectorAll('img[data-src], img.lazy');
            lazyImages.forEach((img) => {
              observerRef.current?.observe(img);
            });
            
            // Check if the added node itself is a lazy image
            if (element.matches('img[data-src], img.lazy')) {
              observerRef.current?.observe(element);
            }
          }
        });
      });
    });

    mutationObserverRef.current.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }, []);

  // Initialize all optimizations
  useEffect(() => {
    const initOptimizations = () => {
      optimizeLCP();
      optimizeFID();
      optimizeCLS();
      setupAdvancedLazyLoading();
      setupMutationObserver();
    };

    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initOptimizations);
    } else {
      initOptimizations();
    }

    return () => {
      document.removeEventListener('DOMContentLoaded', initOptimizations);
      observerRef.current?.disconnect();
      mutationObserverRef.current?.disconnect();
    };
  }, [optimizeLCP, optimizeFID, optimizeCLS, setupAdvancedLazyLoading, setupMutationObserver]);

  return (
    <>
      {/* Resource hints for better performance */}
      {enableResourceHints && (
        <>
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
          <link rel="dns-prefetch" href="//fonts.googleapis.com" />
          <link rel="dns-prefetch" href="//fonts.gstatic.com" />
        </>
      )}

      {/* Critical CSS for Core Web Vitals */}
      <style jsx>{`
        /* Prevent layout shift for images */
        img {
          max-width: 100%;
          height: auto;
        }
        
        /* Lazy loading styles */
        img.lazy {
          opacity: 0;
          transition: opacity 0.3s ease-in-out;
        }
        
        img.lazy.loaded {
          opacity: 1;
        }
        
        /* Optimize font loading */
        @font-face {
          font-display: swap;
        }
        
        /* Contain layout for dynamic content */
        [data-dynamic-content] {
          contain: layout style;
        }
        
        /* Optimize animations for better performance */
        .animate-optimized {
          will-change: transform;
          transform: translateZ(0);
        }
        
        /* Prevent layout shift for hero sections */
        .hero-section {
          contain: layout style paint;
          min-height: 100vh;
        }
      `}</style>

      {/* Core Web Vitals monitoring script */}
      <Script
        id="core-web-vitals-enhancer"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            // Enhanced Core Web Vitals monitoring
            if (typeof window !== 'undefined') {
              // Monitor long tasks that could affect FID
              if ('PerformanceObserver' in window) {
                try {
                  const longTaskObserver = new PerformanceObserver((list) => {
                    list.getEntries().forEach((entry) => {
                      if (entry.duration > 50) {
                        console.warn('Long task detected (affects FID):', {
                          duration: entry.duration,
                          startTime: entry.startTime,
                          name: entry.name
                        });
                      }
                    });
                  });
                  longTaskObserver.observe({ entryTypes: ['longtask'] });
                } catch (e) {
                  // Long task API not supported
                }
              }
              
              // Monitor largest contentful paint
              if ('PerformanceObserver' in window) {
                try {
                  const lcpObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    console.log('LCP detected:', {
                      value: lastEntry.startTime,
                      element: lastEntry.element,
                      url: lastEntry.url
                    });
                  });
                  lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
                } catch (e) {
                  // LCP API not supported
                }
              }
            }
          `,
        }}
      />
    </>
  );
}

// Export utility functions
export const coreWebVitalsUtils = {
  // Preload critical resource
  preloadResource: (href: string, as: string, type?: string) => {
    const link = document.createElement("link");
    link.rel = "preload";
    link.href = href;
    link.as = as;
    if (type) link.type = type;
    document.head.appendChild(link);
  },

  // Optimize image for LCP
  optimizeImageForLCP: (img: HTMLImageElement) => {
    img.fetchPriority = "high";
    img.loading = "eager";
    img.decoding = "sync";
  },

  // Break up long task
  breakUpTask: (callback: () => void, priority: 'user-blocking' | 'user-visible' | 'background' = 'user-visible') => {
    if ('scheduler' in window && 'postTask' in (window as any).scheduler) {
      (window as any).scheduler.postTask(callback, { priority });
    } else {
      setTimeout(callback, 0);
    }
  },

  // Prevent layout shift for element
  preventLayoutShift: (element: HTMLElement, minHeight?: string) => {
    element.style.contain = 'layout';
    if (minHeight) {
      element.style.minHeight = minHeight;
    }
  },
};
