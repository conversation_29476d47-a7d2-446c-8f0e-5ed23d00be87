"use client";

import React, { useEffect, useState, useCallback } from "react";
import Script from "next/script";

// Declare gtag function for Google Analytics
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}

// Type for gtag function
const gtag = typeof window !== 'undefined' ? window.gtag : undefined;

// Types for web-vitals
interface Metric {
  id: string;
  name: string;
  value: number;
  delta: number;
  entries: any[];
}

type MetricHandler = (metric: Metric) => void;

interface PerformanceMetrics {
  // Core Web Vitals
  lcp: number | null; // Largest Contentful Paint
  fid: number | null; // First Input Delay
  cls: number | null; // Cumulative Layout Shift
  fcp: number | null; // First Contentful Paint
  ttfb: number | null; // Time to First Byte
  
  // Additional metrics
  domContentLoaded: number | null;
  loadComplete: number | null;
  navigationStart: number | null;
  
  // Resource metrics
  totalResources: number;
  totalSize: number;
  imageCount: number;
  scriptCount: number;
  stylesheetCount: number;
}

interface PerformanceMonitorProps {
  enableRealTimeMonitoring?: boolean;
  enableAnalyticsReporting?: boolean;
  enableConsoleLogging?: boolean;
  enableVisualIndicator?: boolean;
  thresholds?: {
    lcp: number; // Good: < 2.5s
    fid: number; // Good: < 100ms
    cls: number; // Good: < 0.1
  };
}

/**
 * Comprehensive Performance Monitor Component
 * Tracks Core Web Vitals and other performance metrics
 */
export default function PerformanceMonitor({
  enableRealTimeMonitoring = true,
  enableAnalyticsReporting = true,
  enableConsoleLogging = process.env.NODE_ENV === "development",
  enableVisualIndicator = process.env.NODE_ENV === "development",
  thresholds = {
    lcp: 2500, // 2.5 seconds
    fid: 100,  // 100 milliseconds
    cls: 0.1,  // 0.1
  },
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    lcp: null,
    fid: null,
    cls: null,
    fcp: null,
    ttfb: null,
    domContentLoaded: null,
    loadComplete: null,
    navigationStart: null,
    totalResources: 0,
    totalSize: 0,
    imageCount: 0,
    scriptCount: 0,
    stylesheetCount: 0,
  });

  // Send metrics to analytics
  const sendToAnalytics = useCallback((metricName: string, value: number, id?: string) => {
    if (!enableAnalyticsReporting) return;

    // Google Analytics 4
    if (gtag) {
      gtag("event", metricName, {
        event_category: "Web Vitals",
        event_label: id || "unknown",
        value: Math.round(metricName === "CLS" ? value * 1000 : value),
        non_interaction: true,
        custom_parameter_1: navigator.userAgent,
        custom_parameter_2: window.location.pathname,
      });
    }

    // Custom analytics endpoint
    if (process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT) {
      fetch(process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          metric: metricName,
          value,
          id,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: Date.now(),
        }),
      }).catch((error) => {
        console.warn("Failed to send analytics:", error);
      });
    }
  }, [enableAnalyticsReporting]);

  // Log metrics to console
  const logMetric = useCallback((metricName: string, value: number, rating: string) => {
    if (!enableConsoleLogging) return;

    const color = rating === "good" ? "green" : rating === "needs-improvement" ? "orange" : "red";
    console.log(
      `%c${metricName}: ${Math.round(value)}${metricName === "CLS" ? "" : "ms"} (${rating})`,
      `color: ${color}; font-weight: bold;`
    );
  }, [enableConsoleLogging]);

  // Get performance rating
  const getPerformanceRating = useCallback((metricName: string, value: number): string => {
    switch (metricName) {
      case "LCP":
        return value <= 2500 ? "good" : value <= 4000 ? "needs-improvement" : "poor";
      case "FID":
        return value <= 100 ? "good" : value <= 300 ? "needs-improvement" : "poor";
      case "CLS":
        return value <= 0.1 ? "good" : value <= 0.25 ? "needs-improvement" : "poor";
      case "FCP":
        return value <= 1800 ? "good" : value <= 3000 ? "needs-improvement" : "poor";
      case "TTFB":
        return value <= 800 ? "good" : value <= 1800 ? "needs-improvement" : "poor";
      default:
        return "unknown";
    }
  }, []);

  // Track Core Web Vitals
  const trackWebVitals = useCallback(() => {
    if (typeof window === "undefined") return;

    // Dynamic import to reduce bundle size
    import("web-vitals").then(({ getCLS, getFID, getFCP, getLCP, getTTFB }: any) => {
      getCLS((metric: Metric) => {
        const rating = getPerformanceRating("CLS", metric.value);
        setMetrics((prev) => ({ ...prev, cls: metric.value }));
        sendToAnalytics("CLS", metric.value, metric.id);
        logMetric("CLS", metric.value, rating);
      });

      getFID((metric: Metric) => {
        const rating = getPerformanceRating("FID", metric.value);
        setMetrics((prev) => ({ ...prev, fid: metric.value }));
        sendToAnalytics("FID", metric.value, metric.id);
        logMetric("FID", metric.value, rating);
      });

      getFCP((metric: Metric) => {
        const rating = getPerformanceRating("FCP", metric.value);
        setMetrics((prev) => ({ ...prev, fcp: metric.value }));
        sendToAnalytics("FCP", metric.value, metric.id);
        logMetric("FCP", metric.value, rating);
      });

      getLCP((metric: Metric) => {
        const rating = getPerformanceRating("LCP", metric.value);
        setMetrics((prev) => ({ ...prev, lcp: metric.value }));
        sendToAnalytics("LCP", metric.value, metric.id);
        logMetric("LCP", metric.value, rating);
      });

      getTTFB((metric: Metric) => {
        const rating = getPerformanceRating("TTFB", metric.value);
        setMetrics((prev) => ({ ...prev, ttfb: metric.value }));
        sendToAnalytics("TTFB", metric.value, metric.id);
        logMetric("TTFB", metric.value, rating);
      });
    }).catch((error) => {
      console.warn("Failed to load web-vitals:", error);
    });
  }, [getPerformanceRating, sendToAnalytics, logMetric]);

  // Track navigation timing
  const trackNavigationTiming = useCallback(() => {
    if (typeof window === "undefined" || !window.performance) return;

    const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming;
    if (!navigation) return;

    const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.startTime;
    const loadComplete = navigation.loadEventEnd - navigation.startTime;
    const navigationStart = navigation.startTime;

    setMetrics((prev) => ({
      ...prev,
      domContentLoaded,
      loadComplete,
      navigationStart,
    }));

    if (enableConsoleLogging) {
      console.log("Navigation Timing:", {
        "DOM Content Loaded": `${Math.round(domContentLoaded)}ms`,
        "Load Complete": `${Math.round(loadComplete)}ms`,
        "DNS Lookup": `${Math.round(navigation.domainLookupEnd - navigation.domainLookupStart)}ms`,
        "TCP Connection": `${Math.round(navigation.connectEnd - navigation.connectStart)}ms`,
        "Request": `${Math.round(navigation.responseStart - navigation.requestStart)}ms`,
        "Response": `${Math.round(navigation.responseEnd - navigation.responseStart)}ms`,
      });
    }
  }, [enableConsoleLogging]);

  // Track resource timing
  const trackResourceTiming = useCallback(() => {
    if (typeof window === "undefined" || !window.performance) return;

    const resources = performance.getEntriesByType("resource") as PerformanceResourceTiming[];
    
    let totalSize = 0;
    let imageCount = 0;
    let scriptCount = 0;
    let stylesheetCount = 0;

    resources.forEach((resource) => {
      totalSize += resource.transferSize || 0;
      
      if (resource.initiatorType === "img") imageCount++;
      else if (resource.initiatorType === "script") scriptCount++;
      else if (resource.initiatorType === "link" && resource.name.includes(".css")) stylesheetCount++;
    });

    setMetrics((prev) => ({
      ...prev,
      totalResources: resources.length,
      totalSize,
      imageCount,
      scriptCount,
      stylesheetCount,
    }));

    if (enableConsoleLogging) {
      console.log("Resource Summary:", {
        "Total Resources": resources.length,
        "Total Size": `${Math.round(totalSize / 1024)}KB`,
        "Images": imageCount,
        "Scripts": scriptCount,
        "Stylesheets": stylesheetCount,
      });
    }
  }, [enableConsoleLogging]);

  // Initialize monitoring
  useEffect(() => {
    if (!enableRealTimeMonitoring) return;

    const initMonitoring = () => {
      trackWebVitals();
      trackNavigationTiming();
      trackResourceTiming();
    };

    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", initMonitoring);
      window.addEventListener("load", () => {
        setTimeout(() => {
          trackNavigationTiming();
          trackResourceTiming();
        }, 0);
      });
    } else {
      initMonitoring();
    }

    return () => {
      document.removeEventListener("DOMContentLoaded", initMonitoring);
    };
  }, [enableRealTimeMonitoring, trackWebVitals, trackNavigationTiming, trackResourceTiming]);

  // Visual performance indicator
  const getOverallRating = (): "good" | "needs-improvement" | "poor" => {
    const { lcp, fid, cls } = metrics;
    
    if (lcp === null || fid === null || cls === null) return "needs-improvement";
    
    const lcpRating = getPerformanceRating("LCP", lcp);
    const fidRating = getPerformanceRating("FID", fid);
    const clsRating = getPerformanceRating("CLS", cls);
    
    if (lcpRating === "good" && fidRating === "good" && clsRating === "good") {
      return "good";
    } else if (lcpRating === "poor" || fidRating === "poor" || clsRating === "poor") {
      return "poor";
    } else {
      return "needs-improvement";
    }
  };

  return (
    <>
      {/* Performance monitoring script */}
      <Script
        id="performance-monitor"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            // Additional performance monitoring
            if (typeof window !== 'undefined' && window.performance) {
              // Monitor long tasks
              if ('PerformanceObserver' in window) {
                try {
                  const longTaskObserver = new PerformanceObserver((list) => {
                    list.getEntries().forEach((entry) => {
                      if (entry.duration > 50) {
                        console.warn('Long task detected:', entry.duration + 'ms');
                      }
                    });
                  });
                  longTaskObserver.observe({ entryTypes: ['longtask'] });
                } catch (e) {
                  // Long task API not supported
                }
              }
            }
          `,
        }}
      />

      {/* Visual performance indicator (development only) */}
      {enableVisualIndicator && (
        <div
          style={{
            position: "fixed",
            top: 10,
            right: 10,
            background: "rgba(0,0,0,0.9)",
            color: "white",
            padding: "12px",
            borderRadius: "8px",
            fontSize: "11px",
            zIndex: 9999,
            fontFamily: "monospace",
            minWidth: "200px",
            boxShadow: "0 4px 12px rgba(0,0,0,0.3)",
          }}
        >
          <div style={{ 
            marginBottom: "8px", 
            fontWeight: "bold",
            color: getOverallRating() === "good" ? "#10b981" : 
                  getOverallRating() === "needs-improvement" ? "#f59e0b" : "#ef4444"
          }}>
            Performance: {getOverallRating().toUpperCase()}
          </div>
          
          <div>LCP: {metrics.lcp ? `${Math.round(metrics.lcp)}ms` : "..."}</div>
          <div>FID: {metrics.fid ? `${Math.round(metrics.fid)}ms` : "..."}</div>
          <div>CLS: {metrics.cls ? metrics.cls.toFixed(3) : "..."}</div>
          <div>FCP: {metrics.fcp ? `${Math.round(metrics.fcp)}ms` : "..."}</div>
          <div>TTFB: {metrics.ttfb ? `${Math.round(metrics.ttfb)}ms` : "..."}</div>
          
          <hr style={{ margin: "8px 0", border: "1px solid #333" }} />
          
          <div>Resources: {metrics.totalResources}</div>
          <div>Size: {Math.round(metrics.totalSize / 1024)}KB</div>
          <div>Images: {metrics.imageCount}</div>
          <div>Scripts: {metrics.scriptCount}</div>
        </div>
      )}
    </>
  );
}

// Export performance utilities
export const performanceMonitorUtils = {
  // Get current performance metrics
  getCurrentMetrics: (): Promise<PerformanceMetrics> => {
    return new Promise((resolve) => {
      import("web-vitals").then(({ getCLS, getFID, getFCP, getLCP, getTTFB }: any) => {
        const metrics: Partial<PerformanceMetrics> = {};

        getCLS((metric: Metric) => { metrics.cls = metric.value; });
        getFID((metric: Metric) => { metrics.fid = metric.value; });
        getFCP((metric: Metric) => { metrics.fcp = metric.value; });
        getLCP((metric: Metric) => { metrics.lcp = metric.value; });
        getTTFB((metric: Metric) => { metrics.ttfb = metric.value; });
        
        setTimeout(() => {
          resolve(metrics as PerformanceMetrics);
        }, 100);
      });
    });
  },

  // Generate performance report
  generateReport: async (): Promise<string> => {
    const metrics = await performanceMonitorUtils.getCurrentMetrics();
    
    return `
Performance Report
==================
LCP: ${metrics.lcp ? Math.round(metrics.lcp) + 'ms' : 'N/A'}
FID: ${metrics.fid ? Math.round(metrics.fid) + 'ms' : 'N/A'}
CLS: ${metrics.cls ? metrics.cls.toFixed(3) : 'N/A'}
FCP: ${metrics.fcp ? Math.round(metrics.fcp) + 'ms' : 'N/A'}
TTFB: ${metrics.ttfb ? Math.round(metrics.ttfb) + 'ms' : 'N/A'}

Generated at: ${new Date().toISOString()}
URL: ${window.location.href}
User Agent: ${navigator.userAgent}
    `.trim();
  },
};
