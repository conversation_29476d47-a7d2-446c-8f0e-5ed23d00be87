"use client";

import React, { useEffect, useCallback, Suspense } from "react";
import <PERSON>ript from "next/script";
import { usePathname, useSearchParams } from "next/navigation";

interface AnalyticsProviderProps {
  googleAnalyticsId?: string;
  googleTagManagerId?: string;
  facebookPixelId?: string;
  enableHotjar?: boolean;
  enableClarityMicrosoft?: boolean;
  enableCustomAnalytics?: boolean;
  customAnalyticsEndpoint?: string;
  children: React.ReactNode;
}

// Extend window object for analytics
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
    dataLayer: any[];
    fbq: (...args: any[]) => void;
    hj: (...args: any[]) => void;
    clarity: (...args: any[]) => void;
  }
}

// Loading component for AnalyticsProvider
const AnalyticsProviderLoading = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};

/**
 * Comprehensive Analytics Provider Content
 * Handles Google Analytics, GTM, Facebook Pixel, and other tracking services
 */
function AnalyticsProviderContent({
  googleAnalyticsId = process.env.NEXT_PUBLIC_GA_ID,
  googleTagManagerId = process.env.NEXT_PUBLIC_GTM_ID,
  facebookPixelId = process.env.NEXT_PUBLIC_FB_PIXEL_ID,
  enableHotjar = false,
  enableClarityMicrosoft = false,
  enableCustomAnalytics = false,
  customAnalyticsEndpoint = process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT,
  children,
}: AnalyticsProviderProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Track page views
  const trackPageView = useCallback((url: string) => {
    // Google Analytics
    if (googleAnalyticsId && typeof window.gtag !== "undefined") {
      window.gtag("config", googleAnalyticsId, {
        page_path: url,
        custom_map: {
          custom_parameter_1: "page_category",
          custom_parameter_2: "user_type",
        },
      });
    }

    // Facebook Pixel
    if (facebookPixelId && typeof window.fbq !== "undefined") {
      window.fbq("track", "PageView");
    }

    // Custom Analytics
    if (enableCustomAnalytics && customAnalyticsEndpoint) {
      fetch(customAnalyticsEndpoint, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          event: "page_view",
          url,
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          referrer: document.referrer,
          screenResolution: `${screen.width}x${screen.height}`,
          viewportSize: `${window.innerWidth}x${window.innerHeight}`,
        }),
      }).catch((error) => {
        console.warn("Failed to send custom analytics:", error);
      });
    }
  }, [googleAnalyticsId, facebookPixelId, enableCustomAnalytics, customAnalyticsEndpoint]);

  // Track custom events
  const trackEvent = useCallback((
    eventName: string,
    parameters: Record<string, any> = {}
  ) => {
    // Google Analytics
    if (googleAnalyticsId && typeof window.gtag !== "undefined") {
      window.gtag("event", eventName, {
        event_category: parameters.category || "engagement",
        event_label: parameters.label,
        value: parameters.value,
        ...parameters,
      });
    }

    // Facebook Pixel
    if (facebookPixelId && typeof window.fbq !== "undefined") {
      window.fbq("track", eventName, parameters);
    }

    // Custom Analytics
    if (enableCustomAnalytics && customAnalyticsEndpoint) {
      fetch(customAnalyticsEndpoint, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          event: eventName,
          parameters,
          timestamp: Date.now(),
          url: window.location.href,
        }),
      }).catch((error) => {
        console.warn("Failed to send custom event:", error);
      });
    }
  }, [googleAnalyticsId, facebookPixelId, enableCustomAnalytics, customAnalyticsEndpoint]);

  // Track e-commerce events
  const trackPurchase = useCallback((
    transactionId: string,
    value: number,
    currency: string = "PKR",
    items: any[] = []
  ) => {
    // Google Analytics Enhanced Ecommerce
    if (googleAnalyticsId && typeof window.gtag !== "undefined") {
      window.gtag("event", "purchase", {
        transaction_id: transactionId,
        value,
        currency,
        items,
      });
    }

    // Facebook Pixel
    if (facebookPixelId && typeof window.fbq !== "undefined") {
      window.fbq("track", "Purchase", {
        value,
        currency,
        content_ids: items.map(item => item.item_id),
        content_type: "product",
      });
    }
  }, [googleAnalyticsId, facebookPixelId]);

  // Track route changes
  useEffect(() => {
    const url = pathname + searchParams.toString();
    trackPageView(url);
  }, [pathname, searchParams, trackPageView]);

  // Initialize analytics services
  useEffect(() => {
    // Initialize Facebook Pixel
    if (facebookPixelId && typeof window.fbq !== "undefined") {
      window.fbq("init", facebookPixelId);
    }

    // Initialize Hotjar
    if (enableHotjar && typeof window.hj !== "undefined") {
      window.hj("identify", "user_id", {
        // Add user properties here
      });
    }

    // Initialize Microsoft Clarity
    if (enableClarityMicrosoft && typeof window.clarity !== "undefined") {
      window.clarity("set", "page_category", getPageCategory(pathname));
    }
  }, [facebookPixelId, enableHotjar, enableClarityMicrosoft, pathname]);

  // Get page category for analytics
  const getPageCategory = (path: string): string => {
    if (path === "/") return "home";
    if (path.startsWith("/products")) return "products";
    if (path.startsWith("/about")) return "about";
    if (path.startsWith("/contact")) return "contact";
    if (path.startsWith("/blogs")) return "blog";
    if (path.startsWith("/cart")) return "cart";
    if (path.startsWith("/checkout")) return "checkout";
    return "other";
  };

  return (
    <>
      {/* Google Analytics */}
      {googleAnalyticsId && (
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${googleAnalyticsId}`}
            strategy="afterInteractive"
          />
          <Script
            id="google-analytics"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${googleAnalyticsId}', {
                  page_path: window.location.pathname,
                  anonymize_ip: true,
                  allow_google_signals: false,
                  allow_ad_personalization_signals: false,
                  custom_map: {
                    'custom_parameter_1': 'page_category',
                    'custom_parameter_2': 'user_type'
                  }
                });
              `,
            }}
          />
        </>
      )}

      {/* Google Tag Manager */}
      {googleTagManagerId && (
        <>
          <Script
            id="google-tag-manager"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','${googleTagManagerId}');
              `,
            }}
          />
          <noscript>
            <iframe
              src={`https://www.googletagmanager.com/ns.html?id=${googleTagManagerId}`}
              height="0"
              width="0"
              style={{ display: "none", visibility: "hidden" }}
            />
          </noscript>
        </>
      )}

      {/* Facebook Pixel */}
      {facebookPixelId && (
        <Script
          id="facebook-pixel"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              !function(f,b,e,v,n,t,s)
              {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
              n.callMethod.apply(n,arguments):n.queue.push(arguments)};
              if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
              n.queue=[];t=b.createElement(e);t.async=!0;
              t.src=v;s=b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t,s)}(window, document,'script',
              'https://connect.facebook.net/en_US/fbevents.js');
              fbq('init', '${facebookPixelId}');
              fbq('track', 'PageView');
            `,
          }}
        />
      )}

      {/* Hotjar */}
      {enableHotjar && (
        <Script
          id="hotjar"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(h,o,t,j,a,r){
                h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                h._hjSettings={hjid:${process.env.NEXT_PUBLIC_HOTJAR_ID || 0},hjsv:6};
                a=o.getElementsByTagName('head')[0];
                r=o.createElement('script');r.async=1;
                r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                a.appendChild(r);
              })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
            `,
          }}
        />
      )}

      {/* Microsoft Clarity */}
      {enableClarityMicrosoft && (
        <Script
          id="microsoft-clarity"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
              })(window, document, "clarity", "script", "${process.env.NEXT_PUBLIC_CLARITY_ID || ''}");
            `,
          }}
        />
      )}

      {/* Performance and Error Tracking */}
      <Script
        id="performance-tracking"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            // Track JavaScript errors
            window.addEventListener('error', function(e) {
              if (typeof gtag !== 'undefined') {
                gtag('event', 'exception', {
                  description: e.error ? e.error.toString() : e.message,
                  fatal: false,
                  custom_parameter_1: 'javascript_error'
                });
              }
            });

            // Track unhandled promise rejections
            window.addEventListener('unhandledrejection', function(e) {
              if (typeof gtag !== 'undefined') {
                gtag('event', 'exception', {
                  description: e.reason ? e.reason.toString() : 'Unhandled promise rejection',
                  fatal: false,
                  custom_parameter_1: 'promise_rejection'
                });
              }
            });

            // Track page load performance
            window.addEventListener('load', function() {
              setTimeout(function() {
                const navigation = performance.getEntriesByType('navigation')[0];
                if (navigation && typeof gtag !== 'undefined') {
                  gtag('event', 'timing_complete', {
                    name: 'page_load',
                    value: Math.round(navigation.loadEventEnd - navigation.navigationStart)
                  });
                }
              }, 0);
            });
          `,
        }}
      />

      {children}
    </>
  );
}

// Export analytics utilities
export const analyticsUtils = {
  // Track custom event
  trackEvent: (eventName: string, parameters: Record<string, any> = {}) => {
    if (typeof window !== "undefined" && typeof window.gtag !== "undefined") {
      window.gtag("event", eventName, parameters);
    }
  },

  // Track page view
  trackPageView: (url: string) => {
    if (typeof window !== "undefined" && typeof window.gtag !== "undefined") {
      window.gtag("config", process.env.NEXT_PUBLIC_GA_ID || "", {
        page_path: url,
      });
    }
  },

  // Track conversion
  trackConversion: (conversionId: string, value?: number) => {
    if (typeof window !== "undefined" && typeof window.gtag !== "undefined") {
      window.gtag("event", "conversion", {
        send_to: conversionId,
        value: value,
        currency: "PKR",
      });
    }
  },

  // Track e-commerce purchase
  trackPurchase: (transactionId: string, value: number, items: any[]) => {
    if (typeof window !== "undefined" && typeof window.gtag !== "undefined") {
      window.gtag("event", "purchase", {
        transaction_id: transactionId,
        value,
        currency: "PKR",
        items,
      });
    }
  },

  // Track product view
  trackProductView: (productId: string, productName: string, category: string, value: number) => {
    if (typeof window !== "undefined" && typeof window.gtag !== "undefined") {
      window.gtag("event", "view_item", {
        currency: "PKR",
        value,
        items: [{
          item_id: productId,
          item_name: productName,
          category,
          quantity: 1,
          price: value,
        }],
      });
    }
  },

  // Track add to cart
  trackAddToCart: (productId: string, productName: string, category: string, value: number, quantity: number = 1) => {
    if (typeof window !== "undefined" && typeof window.gtag !== "undefined") {
      window.gtag("event", "add_to_cart", {
        currency: "PKR",
        value: value * quantity,
        items: [{
          item_id: productId,
          item_name: productName,
          category,
          quantity,
          price: value,
        }],
      });
    }
  },
};

/**
 * Main AnalyticsProvider component with Suspense boundary
 */
export default function AnalyticsProvider({
  googleAnalyticsId = process.env.NEXT_PUBLIC_GA_ID,
  googleTagManagerId = process.env.NEXT_PUBLIC_GTM_ID,
  facebookPixelId = process.env.NEXT_PUBLIC_FB_PIXEL_ID,
  enableHotjar = false,
  enableClarityMicrosoft = false,
  enableCustomAnalytics = false,
  customAnalyticsEndpoint = process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT,
  children,
}: AnalyticsProviderProps) {
  return (
    <Suspense fallback={<AnalyticsProviderLoading>{children}</AnalyticsProviderLoading>}>
      <AnalyticsProviderContent
        googleAnalyticsId={googleAnalyticsId}
        googleTagManagerId={googleTagManagerId}
        facebookPixelId={facebookPixelId}
        enableHotjar={enableHotjar}
        enableClarityMicrosoft={enableClarityMicrosoft}
        enableCustomAnalytics={enableCustomAnalytics}
        customAnalyticsEndpoint={customAnalyticsEndpoint}
      >
        {children}
      </AnalyticsProviderContent>
    </Suspense>
  );
}
