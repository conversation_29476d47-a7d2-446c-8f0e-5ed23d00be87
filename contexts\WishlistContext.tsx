"use client";
import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { toast } from "sonner";
import { ProductData } from "@/types";

interface WishlistContextType {
  wishlist: string[];
  wishlistItems: ProductData[];
  addToWishlist: (productId: string) => void;
  removeFromWishlist: (productId: string) => void;
  isInWishlist: (productId: string) => boolean;
  clearWishlist: () => void;
  updateWishlistItems: (products: ProductData[]) => void;
}

const WishlistContext = createContext<WishlistContextType | undefined>(
  undefined
);

export const WishlistProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  // Store only product IDs in the wishlist
  const [wishlist, setWishlist] = useState<string[]>([]);
  // Store the actual product data for display
  const [wishlistItems, setWishlistItems] = useState<ProductData[]>([]);

  // Load wishlist from localStorage on initial render
  useEffect(() => {
    const savedWishlist = localStorage.getItem("wishlist");
    if (savedWishlist) {
      try {
        const parsedWishlist = JSON.parse(savedWishlist);
        if (Array.isArray(parsedWishlist)) {
          setWishlist(parsedWishlist);
        }
      } catch (error) {
        console.error("Error parsing wishlist from localStorage:", error);
      }
    }
  }, []);

  // Save wishlist to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem("wishlist", JSON.stringify(wishlist));
  }, [wishlist]);

  // Add a product to the wishlist
  const addToWishlist = (productId: string) => {
    if (!isInWishlist(productId)) {
      setWishlist((prev) => [...prev, productId]);
      toast.success("Added to wishlist");
    }
  };

  // Remove a product from the wishlist
  const removeFromWishlist = (productId: string) => {
    setWishlist((prev) => prev.filter((id) => id !== productId));
    toast.success("Removed from wishlist");
  };

  // Check if a product is in the wishlist
  const isInWishlist = (productId: string) => {
    return wishlist.includes(productId);
  };

  // Clear the entire wishlist
  const clearWishlist = () => {
    setWishlist([]);
    toast.success("Wishlist cleared");
  };

  // Update wishlistItems when products are fetched - memoized with useCallback
  const updateWishlistItems = useCallback(
    (products: ProductData[]) => {
      const items = products.filter((product) => wishlist.includes(product.id));
      setWishlistItems(items);
    },
    [wishlist]
  );

  return (
    <WishlistContext.Provider
      value={{
        wishlist,
        wishlistItems,
        addToWishlist,
        removeFromWishlist,
        isInWishlist,
        clearWishlist,
        updateWishlistItems,
      }}
    >
      {children}
    </WishlistContext.Provider>
  );
};

export const useWishlist = () => {
  const context = useContext(WishlistContext);
  if (context === undefined) {
    throw new Error("useWishlist must be used within a WishlistProvider");
  }
  return context;
};
