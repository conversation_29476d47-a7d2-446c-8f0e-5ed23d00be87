"use client";
import React, { useState, useEffect } from "react";
import { useWishlist } from "@/contexts/WishlistContext";
import { getProducts } from "@/apis/products";
import { motion } from "framer-motion";
import { Heart, ShoppingCart, Trash2, AlertCircle } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useCart } from "@/contexts/CartContext";
import { useCurrency } from "@/contexts/CurrencyContext";
import { toast } from "sonner";
import { IMAGE_PLACEHOLDER_URL } from "@/constants/helpers";
import { ProductData } from "@/types";

const WishlistPage = () => {
  const { wishlist, removeFromWishlist, clearWishlist, updateWishlistItems } =
    useWishlist();
  const { addToCart } = useCart();
  const { formatPrice } = useCurrency();
  const [wishlistProducts, setWishlistProducts] = useState<ProductData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Fetch wishlist products
  useEffect(() => {
    // Skip API call if wishlist is empty and we've already initialized
    if (wishlist.length === 0 && isInitialized) {
      setWishlistProducts([]);
      return;
    }

    const fetchWishlistProducts = async () => {
      setIsLoading(true);
      try {
        // Get all products
        const allProducts = await getProducts();

        // Filter products that are in the wishlist
        const products = allProducts.filter((product) =>
          wishlist.includes(product.id)
        );
        setWishlistProducts(products);

        // Update wishlist items in context
        updateWishlistItems(allProducts);

        // Mark as initialized
        setIsInitialized(true);
      } catch (error) {
        console.error("Error fetching wishlist products:", error);
        toast.error("Failed to load wishlist products");
      } finally {
        setIsLoading(false);
      }
    };

    fetchWishlistProducts();
  }, [wishlist, updateWishlistItems, isInitialized]);

  const handleRemoveFromWishlist = (productId: string) => {
    removeFromWishlist(productId);
  };

  const handleAddToCart = (product: ProductData) => {
    addToCart(product);
  };

  const handleClearWishlist = () => {
    if (wishlist.length === 0) return;

    if (confirm("Are you sure you want to clear your wishlist?")) {
      clearWishlist();
    }
  };

  // Only show loading state on initial load, not on subsequent updates
  if (isLoading && !isInitialized) {
    return (
      <div className="container mx-auto px-4 py-16 flex justify-center items-center">
        <div className="animate-pulse">Loading wishlist...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 relative">
      {/* Subtle loading indicator for subsequent updates */}
      {isLoading && isInitialized && (
        <div className="absolute top-4 right-4 flex items-center gap-2 text-xs text-gray-500 bg-white/80 px-3 py-1 rounded-full shadow-sm">
          <div className="h-3 w-3 rounded-full border-2 border-t-accent border-gray-300 animate-spin"></div>
          <span>Updating...</span>
        </div>
      )}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">My Wishlist</h1>
        <p className="text-gray-600">
          {wishlist.length === 0
            ? "Your wishlist is empty"
            : `You have ${wishlist.length} item${
                wishlist.length !== 1 ? "s" : ""
              } in your wishlist`}
        </p>
      </div>

      {wishlist.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          <div className="flex justify-center mb-4">
            <Heart size={48} className="text-gray-300" />
          </div>
          <h2 className="text-xl font-medium mb-2">Your wishlist is empty</h2>
          <p className="text-gray-500 mb-6">
            Add items to your wishlist by clicking the heart icon on products
            you love.
          </p>
          <Link href="/products">
            <Button variant="default">Browse Products</Button>
          </Link>
        </div>
      ) : (
        <>
          <div className="flex justify-end mb-4">
            <Button
              variant="outline"
              size="sm"
              className="text-gray-500 hover:text-red-500 flex items-center gap-1"
              onClick={handleClearWishlist}
            >
              <Trash2 size={16} />
              <span>Clear Wishlist</span>
            </Button>
          </div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            layout
            key="wishlist-grid"
          >
            {wishlistProducts.map((product) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.3 }}
                layout
                layoutId={`wishlist-item-${product.id}`}
                className="bg-white rounded-lg shadow-sm overflow-hidden"
              >
                <div className="relative">
                  <Link href={`/products/${product.id}`}>
                    <div className="relative h-64 w-full overflow-hidden bg-gray-100">
                      <Image
                        src={
                          ((product.images && product.images.length > 0) ||
                          product.image
                            ? product.image || product.image
                            : IMAGE_PLACEHOLDER_URL) as string
                        }
                        alt={product.title || product.name || "Product"}
                        fill
                        className="object-cover object-center transition-transform duration-500 hover:scale-105"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      />

                      {/* Discount badge */}
                      {product.discount && product.discount !== "0" && (
                        <div className="absolute top-3 left-3 z-10 bg-accent text-white text-xs font-bold px-2 py-1 rounded">
                          {product.discount}% OFF
                        </div>
                      )}
                    </div>
                  </Link>

                  {/* Action buttons */}
                  <div className="absolute top-3 right-3 flex flex-col gap-2">
                    <button
                      className="p-2 rounded-full bg-white shadow-sm hover:bg-red-500 hover:text-white transition-colors"
                      onClick={() => handleRemoveFromWishlist(product.id)}
                      aria-label="Remove from wishlist"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>

                <div className="p-4">
                  <Link href={`/products/${product.id}`}>
                    <h3 className="font-medium text-gray-900 mb-1 hover:text-accent transition-colors">
                      {product.title || product.name || "Unnamed Product"}
                    </h3>
                  </Link>

                  <div className="flex items-center justify-between mb-3">
                    {product.discount && product.discount !== "0" ? (
                      <div className="flex items-center">
                        <span className="text-accent font-semibold">
                          {formatPrice(
                            Math.round(
                              product.price - product.price * (parseInt(product.discount) / 100)
                            )
                          )}
                        </span>
                        <span className="ml-2 text-gray-400 text-sm line-through">
                          {formatPrice(product.price)}
                        </span>
                      </div>
                    ) : (
                      <span className="text-accent font-semibold">
                        {formatPrice(product.price)}
                      </span>
                    )}

                    {!product.available && (
                      <span className="text-xs text-red-500 font-medium flex items-center gap-1">
                        <AlertCircle size={12} />
                        Out of stock
                      </span>
                    )}
                  </div>

                  <Button
                    variant="default"
                    size="sm"
                    className="w-full flex items-center justify-center gap-1"
                    onClick={() => handleAddToCart(product)}
                    disabled={!product.available}
                  >
                    <ShoppingCart size={16} />
                    <span>Add to Cart</span>
                  </Button>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </>
      )}
    </div>
  );
};

export default WishlistPage;
