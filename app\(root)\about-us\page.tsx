"use client";
import React from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Award, Users, Heart, Truck } from "lucide-react";
import BreadcrumbJsonLd, { commonBreadcrumbs } from "@/components/SEO/BreadcrumbJsonLd";
import EnhancedBreadcrumbs from "@/components/SEO/EnhancedBreadcrumbs";
import FAQSchema, { businessFAQs } from "@/components/SEO/FAQSchema";
import Script from "next/script";
import { siteConfig, getUrl } from "@/config/site";
import { metadata } from "./metadata";

const AboutUs = () => {
  const features = [
    {
      icon: <Award className="h-8 w-8 text-accent" />,
      title: "Premium Quality",
      description: "Handcrafted furniture made from the finest Pakistani hardwoods using traditional Chinioti techniques passed down through generations."
    },
    {
      icon: <Users className="h-8 w-8 text-accent" />,
      title: "Expert Craftsmen",
      description: "Our skilled artisans from Chiniot have generations of experience in creating beautiful wooden furniture with intricate carvings."
    },
    {
      icon: <Heart className="h-8 w-8 text-accent" />,
      title: "Made with Love",
      description: "Every piece is crafted with passion and attention to detail in Chiniot, ensuring lasting beauty and authentic craftsmanship."
    },
    {
      icon: <Truck className="h-8 w-8 text-accent" />,
      title: "Worldwide Delivery",
      description: "We deliver our exquisite Chiniot furniture pieces to customers around the globe with secure packaging."
    }
  ];

  const stats = [
    { number: "500+", label: "Years of Heritage" },
    { number: "1000+", label: "Happy Customers" },
    { number: "500+", label: "Unique Designs" },
    { number: "50+", label: "Countries Served" }
  ];

  // Organization schema for About page
  const aboutPageSchema = {
    "@context": "https://schema.org",
    "@type": "AboutPage",
    "@id": `${getUrl("/about-us")}/#aboutpage`,
    name: "About Chinioti Wooden Art",
    description: "Learn about our heritage in traditional furniture craftsmanship from Chiniot, Pakistan",
    url: getUrl("/about-us"),
    mainEntity: {
      "@id": `${getUrl()}/#organization`
    },
    inLanguage: "en-US",
    isPartOf: {
      "@type": "WebSite",
      name: siteConfig.name,
      url: getUrl(),
    },
    about: {
      "@type": "Organization",
      name: siteConfig.name,
      description: "Traditional furniture makers from Chiniot, Punjab, Pakistan",
      foundingDate: "2010",
      location: {
        "@type": "Place",
        name: "Chiniot, Punjab, Pakistan",
        geo: {
          "@type": "GeoCoordinates",
          latitude: siteConfig.seo.location.latitude,
          longitude: siteConfig.seo.location.longitude
        }
      },
      specialty: "Handcrafted Wooden Furniture",
      heritage: "500+ Years Traditional Craftsmanship"
    }
  };

  return (
    <>
      {/* SEO Structured Data */}
      <BreadcrumbJsonLd
        items={commonBreadcrumbs.aboutUs}
        currentPage="About Us"
      />

      <Script
        id="about-page-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(aboutPageSchema)
        }}
      />

      <FAQSchema
        faqs={businessFAQs}
        title="About Chinioti Wooden Art - Frequently Asked Questions"
        description="Learn more about our company, location, and services"
      />

      <main className="min-h-screen">
        {/* Enhanced Breadcrumbs */}
        <div className="container mx-auto px-4 pt-8">
          <EnhancedBreadcrumbs
            showHome={true}
            showStructuredData={false} // Already handled above
            className="text-sm"
          />
        </div>

        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-br from-gray-50 to-white">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center max-w-4xl mx-auto"
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                About <span className="text-accent">Chinioti Wooden Art</span>
              </h1>
              <p className="text-lg md:text-xl text-gray-600 mb-8">
                Preserving 500+ years of rich Chinioti craftsmanship heritage while creating timeless handcrafted wooden furniture pieces that bring authentic Pakistani elegance and warmth to homes worldwide.
              </p>
              <div className="text-sm text-gray-500">
                <span>Chiniot, Punjab, Pakistan</span> • <span>Traditional Furniture Makers Since 2010</span>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Story Section */}
        <section className="py-16" aria-labelledby="our-story-heading">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <h2 id="our-story-heading" className="text-3xl md:text-4xl font-bold mb-6">Our Heritage Story</h2>
                <p className="text-gray-600 mb-6">
                  Founded in the heart of Chiniot, Punjab, Pakistan, our company has been dedicated to preserving and promoting the ancient art of wooden furniture making that spans over 500 years. Chiniot has been renowned worldwide for its exquisite woodwork and intricate carvings, and we are proud to continue this legendary craftsmanship legacy.
                </p>
                <p className="text-gray-600 mb-6">
                  Our master craftsmen use traditional techniques passed down through generations of Chinioti artisans, combined with modern design sensibilities to create furniture that is both timeless and contemporary. Each handcrafted piece tells a story of dedication, skill, and passion for the ancient craft of wooden furniture making.
                </p>
                <p className="text-gray-600 mb-6">
                  From intricate hand-carved details to smooth finishes using premium Pakistani hardwoods like Sheesham and Teak, every aspect of our furniture reflects the rich cultural heritage of Chiniot and the commitment to excellence that defines our brand.
                </p>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-lg mb-2">Why Chiniot is Special</h3>
                  <p className="text-gray-600 text-sm">
                    Chiniot, located in Punjab, Pakistan, is globally recognized as the furniture capital of the world. The city's artisans have been perfecting their woodworking skills for centuries, creating furniture that graces homes and palaces worldwide.
                  </p>
                </div>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="relative"
              >
                <Image
                  src="/assets/backgrounds/home-bg.png"
                  alt="Traditional Chinioti craftsman creating handcrafted wooden furniture in Chiniot, Punjab, Pakistan"
                  width={600}
                  height={400}
                  className="rounded-lg shadow-lg"
                />
                <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white p-2 rounded">
                  <p className="text-sm">Master craftsman at work in Chiniot</p>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-gray-50" aria-labelledby="why-choose-us-heading">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 id="why-choose-us-heading" className="text-3xl md:text-4xl font-bold mb-4">Why Choose Chinioti Wooden Art</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                We combine 500+ years of traditional Chinioti craftsmanship with modern quality standards to deliver exceptional handcrafted wooden furniture that stands the test of time and brings authentic Pakistani heritage to your home.
              </p>
            </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="text-center h-full hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <div className="flex justify-center mb-4">
                      {feature.icon}
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-4xl md:text-5xl font-bold text-accent mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

        {/* Mission Section */}
        <section className="py-16 bg-accent text-white" aria-labelledby="our-mission-heading">
          <div className="container mx-auto px-4 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="max-w-4xl mx-auto"
            >
              <h2 id="our-mission-heading" className="text-3xl md:text-4xl font-bold mb-6">Our Mission & Values</h2>
              <p className="text-lg md:text-xl opacity-90 mb-8">
                To preserve and promote the traditional art of Chinioti woodwork while creating beautiful, functional handcrafted furniture that enhances homes around the world. We are committed to sustainable practices, fair trade, and supporting our local artisan community in Chiniot, Punjab, Pakistan.
              </p>
              <div className="grid md:grid-cols-3 gap-6 text-left text-[#ec6504]">
                <div className="bg-white bg-opacity-10 p-6 rounded-lg">
                  <h3 className="font-bold text-lg mb-2">Heritage Preservation</h3>
                  <p className="text-sm opacity-90">Keeping alive the 500+ year tradition of Chiniot furniture craftsmanship</p>
                </div>
                <div className="bg-white bg-opacity-10 p-6 rounded-lg">
                  <h3 className="font-bold text-lg mb-2">Quality Excellence</h3>
                  <p className="text-sm opacity-90">Using premium Pakistani hardwoods and traditional techniques</p>
                </div>
                <div className="bg-white bg-opacity-10 p-6 rounded-lg">
                  <h3 className="font-bold text-lg mb-2">Global Reach</h3>
                  <p className="text-sm opacity-90">Bringing authentic Chiniot furniture to customers worldwide</p>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Additional SEO Content Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="prose prose-lg max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-center mb-8">
                The Chiniot Furniture Legacy
              </h2>
              <div className="grid md:grid-cols-2 gap-8 text-gray-700">
                <div>
                  <h3 className="text-xl font-semibold mb-4">Historical Significance</h3>
                  <p className="mb-4">
                    Chiniot, a historic city in Punjab, Pakistan, has been the epicenter of wooden furniture craftsmanship for over 500 years.
                    The city's artisans have created furniture for Mughal emperors, British colonial administrators, and modern homes worldwide.
                  </p>
                  <p>
                    Our craftsmen continue this proud tradition, using the same time-tested techniques while adapting to contemporary design needs.
                  </p>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-4">Sustainable Practices</h3>
                  <p className="mb-4">
                    We source our wood responsibly from certified Pakistani forests, ensuring that our beautiful furniture doesn't come at the cost of environmental degradation.
                  </p>
                  <p>
                    Every piece is built to last generations, embodying the sustainable philosophy of creating furniture that becomes family heirlooms.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
    </>
  );
};

export default AboutUs;
